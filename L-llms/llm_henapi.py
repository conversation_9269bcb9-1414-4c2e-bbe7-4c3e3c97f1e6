import requests
import os
from PIL import Image
import io
from dotenv import load_dotenv

load_dotenv()

class WeddingPhotoEditor:
    def __init__(self):
        self.api_key = os.environ.get("HDGSB_API_KEY")
        self.base_url = "https://www.henapi.top/v1/images/variations"

    def prepare_image(self, image_path):
        """
        准备图片：确保是PNG格式，方形，小于4MB
        """
        with Image.open(image_path) as img:
            # 转换为RGBA（支持透明度）
            if img.mode != 'RGBA':
                img = img.convert('RGBA')

            # 确保是方形
            width, height = img.size
            size = min(width, height)

            # 从中心裁剪为方形
            left = (width - size) // 2
            top = (height - size) // 2
            right = left + size
            bottom = top + size

            img_square = img.crop((left, top, right, bottom))

            # 调整大小到合适尺寸（如果太大）
            if size > 1024:
                img_square = img_square.resize((1024, 1024), Image.Resampling.LANCZOS)

            # 保存为PNG格式的字节流
            img_bytes = io.BytesIO()
            img_square.save(img_bytes, format='PNG')
            img_bytes.seek(0)

            return img_bytes

    def create_wedding_photo(self, image_path, custom_prompt=None, mask_path=None,
                           size="1024x1024", n=1, response_format="url",model="gpt-image-1"):
        """
        创建婚纱照编辑

        Args:
            image_path: 输入图片路径
            custom_prompt: 自定义提示词，如果不提供则使用默认婚纱照提示词
            mask_path: 可选的遮罩图片路径
            size: 生成图片尺寸 (256x256, 512x512, 1024x1024)
            n: 生成图片数量 (1-10)
            response_format: 返回格式 (url 或 b64_json)
        """

        # 默认婚纱照提示词，强调保持人脸特征
        if custom_prompt is None:
            custom_prompt = (
                "Transform into an elegant wedding photo with the person wearing a beautiful white wedding dress. "
                "Keep the original facial features, skin tone, and facial structure exactly the same. "
                "Add a romantic wedding background with soft lighting, flowers, and dreamy atmosphere. "
                "High quality, professional wedding photography style, maintain facial identity."
            )

        # 准备图片
        try:
            prepared_image = self.prepare_image(image_path)
        except Exception as e:
            return {"error": f"图片准备失败: {str(e)}"}

        # 准备请求数据
        files = [
            ('image', ('wedding_input.png', prepared_image, 'image/png'))
        ]

        # 如果提供了遮罩图片
        if mask_path:
            try:
                prepared_mask = self.prepare_image(mask_path)
                files.append(('mask', ('wedding_mask.png', prepared_mask, 'image/png')))
            except Exception as e:
                return {"error": f"遮罩图片准备失败: {str(e)}"}

        # 请求参数
        data = {
            'prompt': custom_prompt,
            'n': str(n),
            'size': size,
            'response_format': response_format,
            'model': model
        }

        # 请求头
        headers = {
            'Authorization': f'Bearer {self.api_key}'
        }

        try:
            response = requests.post(self.base_url, headers=headers, data=data, files=files)

            # 关闭文件流
            for file_tuple in files:
                file_tuple[1][1].close()

            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "error": f"API请求失败: {response.status_code}",
                    "message": response.text
                }

        except Exception as e:
            return {"error": f"请求异常: {str(e)}"}

# 使用示例
def main():
    editor = WeddingPhotoEditor()

    # 示例调用
    image_path = "/Users/<USER>/Pictures/TX1735_10.jpg"  # 替换为实际的输入图片路径

    # 生成婚纱照
    result = editor.create_wedding_photo(
        image_path=image_path,
        custom_prompt="将人物转换为穿着优雅白色婚纱的新娘，保持原有面部特征和肤色不变，添加浪漫的婚礼背景，专业婚纱摄影风格",
        size="1024x1024",
        n=1,  # 生成2张图片
        response_format="url",
        model="dall-e-2"
    )

    print("生成结果:")
    print(result)

if __name__ == "__main__":
    main()